quarkus.http.port=8084
quarkus.mongodb.database=billing

mp.messaging.incoming.invoices.connector=smallrye-rabbitmq

mp.messaging.outgoing.invoices-confirmations.connector=smallrye-kafka

mp.messaging.incoming.invoices-adjust.connector=smallrye-kafka
mp.messaging.incoming.invoices-adjust.auto.offset.reset=earliest

%prod.quarkus.mongodb.connection-string=mongodb://localhost:27018
%prod.kafka.bootstrap.servers=localhost:9092
%prod.rabbitmq-host=localhost
%prod.rabbitmq-port=5672
%prod.rabbitmq-http-port=15672
%prod.rabbitmq-username=guest
%prod.rabbitmq.password=guest

quarkus.container-image.registry=quay.io
# replace below with your quay.io group!
quarkus.container-image.group=quarkus-in-action
quarkus.container-image.tag=1.0.0

%s2i.quarkus.container-image.registry=
%s2i.quarkus.container-image.group=
%s2i.quarkus.container-image.tag=

quarkus.container-image.builder=openshift
quarkus.openshift.prometheus.generate-service-monitor=false
quarkus.openshift.route.expose=true

quarkus.openshift.env.vars.quarkus-mongodb-connection-string=mongodb://mongodb-billing:27017
quarkus.openshift.env.vars.kafka-bootstrap-servers=kafka:9092
quarkus.openshift.env.vars.rabbitmq-host=rabbitmq