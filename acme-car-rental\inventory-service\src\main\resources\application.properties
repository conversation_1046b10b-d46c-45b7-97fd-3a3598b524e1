quarkus.http.port=8083

quarkus.datasource.db-kind=mysql
quarkus.datasource.username=user
quarkus.datasource.password=pass
%prod.quarkus.datasource.jdbc.url=*************************************

# drop and create the database at startup
quarkus.hibernate-orm.database.generation=drop-and-create

quarkus.smallrye-graphql.ui.always-include=true

quarkus.hibernate-orm.sql-load-script=import.sql

quarkus.container-image.registry=quay.io
# replace below with your quay.io group!
quarkus.container-image.group=quarkus-in-action
quarkus.container-image.tag=1.0.0

quarkus.container-image.builder=openshift

quarkus.openshift.env.vars.quarkus-datasource-jdbc-url=*********************************

quarkus.openshift.prometheus.generate-service-monitor=false

# Expose created service to the world
quarkus.openshift.route.expose=true

%s2i.quarkus.container-image.registry=
%s2i.quarkus.container-image.group=
%s2i.quarkus.container-image.tag=

quarkus.openshift.env.vars.quarkus-otel-exporter-otlp-endpoint=http://jaeger:4317

quarkus.metadata.path=/some-other-path
