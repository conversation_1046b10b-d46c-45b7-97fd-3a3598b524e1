syntax = "proto3";

option java_multiple_files = true;
option java_package = "org.acme.inventory.model";
option java_outer_classname = "InventoryProtos";

package inventory;

message InsertCarRequest {
  string licensePlateNumber = 1;
  string manufacturer = 2;
  string model = 3;
}

message RemoveCarRequest {
  string licensePlateNumber = 1;
}

message CarResponse {
  string licensePlateNumber = 1;
  string manufacturer = 2;
  string model = 3;
  int64 id = 4;
}

service InventoryService {
  rpc add(InsertCarRequest) returns (CarResponse) {}
  rpc remove(RemoveCarRequest) returns (CarResponse) {}
}