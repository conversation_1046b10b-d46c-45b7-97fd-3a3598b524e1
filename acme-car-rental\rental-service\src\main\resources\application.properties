quarkus.http.port=8082

%prod.quarkus.mongodb.connection-string=mongodb://mongo:27017

quarkus.mongodb.database=rental

quarkus.rest-client.reservation.url=http://localhost:8081
%prod.quarkus.mongodb.connection-string=mongodb://localhost:27017
%prod.kafka.bootstrap.servers=localhost:9092

quarkus.container-image.registry=quay.io
# replace below with your quay.io group!
quarkus.container-image.group=quarkus-in-action
quarkus.container-image.tag=1.0.0

%s2i.quarkus.container-image.registry=
%s2i.quarkus.container-image.group=
%s2i.quarkus.container-image.tag=

quarkus.container-image.builder=openshift
quarkus.openshift.prometheus.generate-service-monitor=false
quarkus.openshift.route.expose=true

quarkus.openshift.env.vars.quarkus-rest-client-reservation-url=http://reservation-service
quarkus.openshift.env.vars.quarkus-mongodb-connection-string=mongodb://mongodb-rental:27017
quarkus.openshift.env.vars.kafka-bootstrap-servers=kafka:9092