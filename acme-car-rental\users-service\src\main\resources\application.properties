quarkus.http.auth.permission.all-resources.paths=/*
quarkus.http.auth.permission.all-resources.policy=authenticated
quarkus.oidc.application-type=web_app
quarkus.oidc.logout.path=/logout

%prod.quarkus.oidc.auth-server-url=http://localhost:7777/realms/car-rental
%prod.quarkus.oidc.client-id=users-service
%prod.quarkus.oidc.token-state-manager.split-tokens=true

quarkus.rest-client.reservations.url=http://localhost:8081

quarkus.container-image.registry=quay.io
# replace below with your quay.io group!
quarkus.container-image.group=quarkus-in-action
quarkus.container-image.tag=1.0.0

%s2i.quarkus.container-image.registry=
%s2i.quarkus.container-image.group=
%s2i.quarkus.container-image.tag=

quarkus.container-image.builder=openshift
quarkus.openshift.prometheus.generate-service-monitor=false
quarkus.openshift.route.expose=true

# TODO: Don't forget to insert your Keycloak route here
quarkus.openshift.env.vars.quarkus-oidc-auth-server-url=http://keycloak-mstefank-dev.apps.sandbox-m3.1530.p1.openshiftapps.com/realms/car-rental
quarkus.openshift.env.vars.quarkus-rest-client-reservations-url=http://reservation-service
quarkus.openshift.env.vars.quarkus-otel-exporter-otlp-endpoint=http://jaeger:4317
