quarkus.http.port=8081
quarkus.smallrye-graphql-client.inventory.url=http://localhost:8083/graphql
quarkus.http.test-port=8181
quarkus.oidc.application-type=service

%prod.quarkus.oidc.auth-server-url=http://localhost:7777/realms/car-rental
%prod.quarkus.oidc.client-id=reservation-service
%prod.quarkus.oidc.token-state-manager.split-tokens=true

quarkus.datasource.db-kind=postgresql
quarkus.datasource.username=user
quarkus.datasource.password=pass
%prod.quarkus.datasource.reactive.url=vertx-reactive:postgresql://localhost:5432/reservation

# drop and create the database at startup
quarkus.hibernate-orm.database.generation=drop-and-create

%test.mp.messaging.incoming.invoices-rabbitmq.queue.name=invoices
%test.mp.messaging.incoming.invoices-rabbitmq.exchange.name=invoices

quarkus.rest-client.rental.url=http://localhost:8082

%prod.rabbitmq-host=localhost
%prod.rabbitmq-port=5672
%prod.rabbitmq-http-port=15672
%prod.rabbitmq-username=guest
%prod.rabbitmq.password=guest

quarkus.container-image.registry=quay.io
# replace below with your quay.io group!
quarkus.container-image.group=quarkus-in-action
quarkus.container-image.tag=1.0.0

%s2i.quarkus.container-image.registry=
%s2i.quarkus.container-image.group=
%s2i.quarkus.container-image.tag=

quarkus.container-image.builder=openshift
quarkus.openshift.prometheus.generate-service-monitor=false
quarkus.openshift.route.expose=true

quarkus.openshift.env.vars.quarkus-smallrye-graphql-client-inventory-url=http://inventory-service/graphql
quarkus.openshift.env.vars.quarkus-rest-client-rental-url=http://rental-service
quarkus.openshift.env.vars.quarkus-datasource-reactive-url=vertx-reactive:postgresql://postgres-reservation:5432/reservation
# TODO: Don't forget to insert your Keycloak route here
quarkus.openshift.env.vars.quarkus-oidc-auth-server-url=http://keycloak-mstefank-dev.apps.sandbox-m3.1530.p1.openshiftapps.com/realms/car-rental
quarkus.openshift.env.vars.quarkus-otel-exporter-otlp-endpoint=http://jaeger:4317
quarkus.openshift.env.vars.rabbitmq-host=rabbitmq