{@java.lang.String name}
{@java.time.LocalDate startDate}
{@java.time.LocalDate endDate}

<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Reservations</title>
  <link rel="stylesheet" href="https://cdn.simplecss.org/simple.min.css">
  <script src="https://unpkg.com/htmx.org@1.7.0">
  </script>
</head>
<body>

<header>
  <h1>Reservations</h1>
  <p>For logged-in user: {name}</p>
  <a href="/logout">Log out</a>
</header>

<h2>List of reservations</h2>
<div id="reservations" hx-get="/get" hx-trigger="load">
  <!-- To be replaced by the result of calling /get -->
</div>

<h2>Available cars to rent</h2>
<form hx-get="/available" hx-target="#availability">
  <p>Start date:<input id="startDateInput" type="date"
                       name="startDate" value="{startDate}"/></p>
  <p>End date:<input id="endDateInput" type="date"
                     name="endDate" value="{endDate}"/></p>
  <input type="submit" value="Update list"/>
</form>
<div id="availability" hx-get="/available"
     hx-trigger="load, update-available-cars-list from:body"
     hx-include="[id='startDateInput'],[id='endDateInput']">
  <!-- To be replaced by the result of calling /available -->
</div>

</body>
</html>
