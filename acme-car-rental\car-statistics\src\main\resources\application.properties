# Needed to generate Knative resources
quarkus.kubernetes.deployment-target=knative

# Inventory service URL on localhost
quarkus.smallrye-graphql-client.inventory.url=http://localhost:8083/graphql
# Inventory service location in OpenShift
quarkus.knative.env.vars.quarkus-smallrye-graphql-client-inventory-url=http://inventory-service/graphql

# Needed to configure where Knative pulls the image
quarkus.container-image.registry=image-registry.openshift-image-registry.svc:5000
quarkus.container-image.group=mstefank-dev

# Max concurrent requests to a single pod
quarkus.knative.revision-auto-scaling.container-concurrency=2
